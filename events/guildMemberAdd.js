const { Events } = require('discord.js');
const { mongoClient } = require("../mongo/client.js");
const { optimizedFindOne, optimizedInsertOne } = require('../utils/database-optimizer.js');
const { defaults } = require("../utils/default_db_structures");
const { EmbedBuilder } = require('discord.js');
const { safeHandleDehoist } = require('../utils/dehoistRateLimit');
const { sendStickyNicknameRecoveredLog, sendStickyRolesRecoveredLog, sendLogContainer } = require("../utils/sendLog.js");
const { createMemberJoinContainer } = require('../utils/logContainers.js');
const { logger } = require('../utils/consoleLogger.js');

module.exports = {
    name: Events.GuildMemberAdd,
    async execute(client, member) {
        try {
            console.log('[guildMemberAdd] Event triggered for', member.user.tag);
            if (mongoClient.clientConnected == false) {
                console.log("mongodb not ready yet");
                return
            }
        // PERFORMANCE OPTIMIZATION: Use optimized database operations with monitoring and retry logic
        var guildData = await optimizedFindOne('guilds', { id: member.guild.id });
        if (guildData == null) {
            await optimizedInsertOne('guilds', defaults.guild(member.guild.id));
            guildData = await optimizedFindOne('guilds', { id: member.guild.id });
        }

        var userData = await optimizedFindOne('member', { userId: member.id });
        if (userData == null) {
            await optimizedInsertOne('member', defaults.member({ guildId: member.guild.id, userId: member.id }));
            userData = await optimizedFindOne('member', { userId: member.id });
        }

        // Handle dehoist for new members
        if (guildData.dehoist.nameEnabled) {
            const result = await safeHandleDehoist(member, guildData.dehoist);
            if (result.success && result.cooldown) {
                console.log(`[guildMemberAdd] New member ${member.user.id} dehoisted - ${result.cooldown}min cooldown applied`);
            } else if (result.reason === 'guild_throttle') {
                console.log(`[guildMemberAdd] Guild ${member.guild.id} dehoist throttled for new member`);
            }
        }

        // Standard join logging
        if (guildData.logs.enabled) {
            console.log('[guildMemberAdd] Logging is enabled');
            const channels = guildData.logs.channels.filter(l => l.events.includes("guildMemberAdd")).map(l => member.guild.channels.cache.get(l.id)).filter(ch => ch);

            // Only proceed if there are channels configured for this event
            if (channels.length > 0) {
                // Create Components v2 container for member join
                const container = createMemberJoinContainer({
                    userMention: `<@${member.id}>`,
                    userTag: member.user.tag,
                    userId: member.id,
                    accountAge: `<t:${Math.floor(member.user.createdTimestamp / 1000)}:R>`,
                    memberCount: member.guild.memberCount
                });

                // Send container to all configured channels
                await sendLogContainer(member.guild.id, 'guildMemberAdd', container, client);
            }
        }

        // Sticky roles/nickname - optimized with caching
        const {
            getCachedGuildStickyConfig,
            getCachedMemberStickyData,
            getAssignableRoles
        } = require('../utils/stickyCache.js');

        const stickyConfig = await getCachedGuildStickyConfig(member.guild.id);
        console.log('[sticky] Guild sticky config:', stickyConfig);

        if (stickyConfig.enabled && (stickyConfig.roles.length || stickyConfig.nick)) {
            console.log('[sticky] Sticky is enabled and configured, checking member data...');
            const memberStickyData = await getCachedMemberStickyData(member.user.id, member.guild.id);
            const { roles, nick } = memberStickyData;
            console.log('[sticky] Member sticky data:', memberStickyData);

            // Filter saved roles to only include currently sticky roles
            const currentlyStickyRoles = (roles || []).filter(roleId =>
                stickyConfig.roles.includes(roleId)
            );

            // Use cached role validation for better performance
            const assignableRoles = getAssignableRoles(currentlyStickyRoles, member.guild.id, member.guild);

            console.log('[sticky] Restoring for', member.user.tag, 'saved roles:', roles, 'currently sticky:', currentlyStickyRoles, 'assignable:', assignableRoles, 'nick:', nick);

            // Apply sticky data
            const editData = {};
            if (stickyConfig.roles.length && assignableRoles.length) {
                editData.roles = assignableRoles;
            }
            if (stickyConfig.nick && nick) {
                editData.nick = nick;
            }

            if (Object.keys(editData).length > 0) {
                console.log('[sticky] Applying edit data:', editData);
                member.edit(editData).catch(err => console.error('[sticky] Error restoring:', err));

                // Send specialty sticky logs using centralized logging system (async)
                if (editData.roles) {
                    sendStickyRolesRecoveredLog(
                        member.guild.id,
                        member.id,
                        assignableRoles,
                        client
                    ).catch(err => console.error('[sticky] Error sending roles log:', err));
                }
                if (editData.nick) {
                    sendStickyNicknameRecoveredLog(
                        member.guild.id,
                        member.id,
                        nick,
                        client
                    ).catch(err => console.error('[sticky] Error sending nick log:', err));
                }
            } else {
                console.log('[sticky] No edit data to apply');
            }
        } else {
            console.log('[sticky] Sticky not enabled or not configured - enabled:', stickyConfig.enabled, 'roles:', stickyConfig.roles.length, 'nick:', stickyConfig.nick);
        }
        } catch (error) {
            console.error('[guildMemberAdd] Error processing member add:', error);
            logger.error('guildMemberAdd', `Error processing member add: ${error.message}`, client);
        }
    },
};